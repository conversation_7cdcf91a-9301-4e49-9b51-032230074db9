; 用户界面模块

global mainGui := "", statusText := "", resolutionText := ""

; 创建主界面窗口
CreateGUI() {
	global mainGui, statusText, resolutionText
	mainGui := Gui("+MinSize200x90", "技能助手 v1.0")
	statusText := mainGui.Add("Text", "x10 y10 w180 h20 Center", "状态: 已停止")
	statusText.SetFont("s9 Bold")
	resolutionText := mainGui.Add("Text", "x10 y35 w180 h25 Center", "未检测到目标窗口")
	resolutionText.SetFont("s8")

	; 添加刷新按钮
	refreshBtn := mainGui.Add("Button", "x65 y65 w70 h20", "刷新")
	refreshBtn.OnEvent("Click", (ctrl, *) => UpdateGUI())

	; 设置窗口关闭事件
	mainGui.OnEvent("Close", (gui, *) => (gui.Destroy(), ExitApp()))
	mainGui.Show("w200 h90")
}

; 更新运行状态显示
UpdateStatus() {
	global statusText, isRunning
	statusText.Text := Format("状态: {1}", isRunning ? "运行中" : "已停止")
	statusText.SetFont(isRunning ? "c0x008000" : "c0x800000")
}

; 更新界面信息
UpdateGUI() {
	global resolutionText, targetWin
	UpdateStatus()
	try {
		if WinExist(targetWin) {
			try {
				WinGetClientPos(, , &w, &h, targetWin)
				resolutionText.Text := Format("分辨率: {1} x {2} (已绑定)", w, h)
				resolutionText.SetFont("c0x008000")
			} catch TargetError {
				resolutionText.Text := "窗口检测失败"
				resolutionText.SetFont("c0x800000")
			}
		} else {
			resolutionText.Text := "未检测到游戏进程"
			resolutionText.SetFont("c0x800000")
		}
	} catch OSError as e {
		resolutionText.Text := Format("系统错误: {1}", e.Message)
		resolutionText.SetFont("c0x800000")
	} catch Error as e {
		resolutionText.Text := "进程检测失败"
		resolutionText.SetFont("c0x800000")
	}
}
