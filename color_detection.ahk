#Requires AutoHotkey v2.0

; 颜色检测模块
; 处理技能图标的颜色识别和技能取色管理

global skillColors := Map()						; 技能取色数据
global colorTolerance := 10						; 颜色匹配容差
global colorCache := Map()						; 颜色缓存
global lastColorCheck := 0						; 上次检查时间
global configFile := A_ScriptDir . "\skill_colors.ini"	; 配置文件路径

; F1键取色功能：获取所有技能的颜色数据
ShowAllSkillColorsInfo() {
	global skillPositions, skillColors, baseW, baseH

	; 确保坐标是最新的
	currentRes := UpdateSkillPositions()

	skillColors := Map()							; 清空旧的技能取色
	output := "=== 技能取色信息 ===`n"
	output .= Format("当前分辨率: {1} x {2}", currentRes.width, currentRes.height)
	if (currentRes.width != baseW || currentRes.height != baseH) {
		output .= Format(" (基于 {1} x {2} 换算)", baseW, baseH)
	}
	output .= "`n`n"

	skillOrder := ["1", "2", "3", "4", "5", "q", "r", "t", "g"]

	for skillKey in skillOrder {
		if skillPositions.Has(skillKey) {
			skillInfo := skillPositions[skillKey]
			color := PixelGetColor(skillInfo.x, skillInfo.y)
			skillColors[skillKey] := color			; 保存技能取色

			skillName := skillKey . "技能"
			coords := skillInfo.x . "=" . skillInfo.y
			colorHex := Format("0x{:06X}", color)
			output .= Format("{1:-7s}={2:-13s} 颜色 = {3}", skillName, coords, colorHex) . "`n"

			; 绝技技能有两个检测点
			if (skillKey = "g" && skillInfo.HasOwnProp("x2")) {
				color2 := PixelGetColor(skillInfo.x2, skillInfo.y2)
				skillColors[skillKey . "_2"] := color2

				skillName2 := "g技能2"
				coords2 := skillInfo.x2 . "=" . skillInfo.y2
				colorHex2 := Format("0x{:06X}", color2)
				output .= Format("{1:-7s}={2:-13s} 颜色 = {3}", skillName2, coords2, colorHex2) . "`n"
			}
		}
	}

	output .= "`n取色完成。"
	if SaveSkillColors() {
		output .= "`n配置已自动保存。"
	}

	MsgBox(output, "技能取色完成", 64)
}

; 更新技能颜色缓存
UpdateColorCache() {
	global skillPositions, colorCache

	for skillKey, skillInfo in skillPositions {
		try {
			colorCache[skillKey] := PixelGetColor(skillInfo.x, skillInfo.y)

			; 绝技需要检测两个位置
			if (skillKey = "g" && skillInfo.HasOwnProp("x2")) {
				colorCache[skillKey . "_2"] := PixelGetColor(skillInfo.x2, skillInfo.y2)
			}
		} catch {
			continue								; 取色失败时跳过
		}
	}
}

; 检查技能是否可用
IsSkillAvailableByColor(skillKey, skillInfo) {
	global skillColors, colorTolerance, colorCache

	try {
		if !colorCache.Has(skillKey) {
			return false
		}

		currentColor := colorCache[skillKey]

		; 绝技需要检查两个位置
		if (skillKey = "g" && skillInfo.HasOwnProp("x2")) {
			if !colorCache.Has(skillKey . "_2") {
				return false
			}

			currentColor2 := colorCache[skillKey . "_2"]

			if skillColors.Has(skillKey) && skillColors.Has(skillKey . "_2") {
				skillColor1 := skillColors[skillKey]
				skillColor2 := skillColors[skillKey . "_2"]

				return IsColorSimilar(currentColor, skillColor1, colorTolerance) &&
					   IsColorSimilar(currentColor2, skillColor2, colorTolerance)
			}
			return false
		}

		; 普通技能检查
		if skillColors.Has(skillKey) {
			skillColor := skillColors[skillKey]
			return IsColorSimilar(currentColor, skillColor, colorTolerance)
		}

		return false

	} catch Error as e {
		return false
	}
}

; 判断两个颜色是否相似
IsColorSimilar(color1, color2, tolerance) {
	; 提取RGB分量
	r1 := (color1 >> 16) & 0xFF
	g1 := (color1 >> 8) & 0xFF
	b1 := color1 & 0xFF

	r2 := (color2 >> 16) & 0xFF
	g2 := (color2 >> 8) & 0xFF
	b2 := color2 & 0xFF

	; 计算RGB差值
	rDiff := Abs(r1 - r2)
	gDiff := Abs(g1 - g2)
	bDiff := Abs(b1 - b2)

	; 检查差值是否在容差范围内
	return (rDiff <= tolerance) && (gDiff <= tolerance) && (bDiff <= tolerance)
}

; 保存技能取色到配置文件
SaveSkillColors() {
	global skillColors, configFile, colorTolerance

	try {
		IniWrite(colorTolerance, configFile, "Settings", "ColorTolerance")

		for skillKey, color in skillColors {
			colorHex := Format("0x{:06X}", color)
			IniWrite(colorHex, configFile, "SkillColors", skillKey)
		}

		IniWrite(A_Now, configFile, "Settings", "SaveTime")
		return true

	} catch OSError {
		return false
	} catch Error {
		return false
	}
}

; 从配置文件加载技能取色
LoadSkillColors() {
	global skillColors, configFile, colorTolerance, skillPositions

	if !FileExist(configFile) {
		return false
	}

	try {
		skillColors := Map()
		colorTolerance := IniRead(configFile, "Settings", "ColorTolerance", 10)

		loadedCount := 0
		for skillKey, skillInfo in skillPositions {
			try {
				colorHex := IniRead(configFile, "SkillColors", skillKey, "")
				if (colorHex != "") {
					skillColors[skillKey] := Integer(colorHex)
					loadedCount++
				}

				; 绝技的第二个检测点
				if (skillKey = "g") {
					colorHex2 := IniRead(configFile, "SkillColors", skillKey . "_2", "")
					if (colorHex2 != "") {
						skillColors[skillKey . "_2"] := Integer(colorHex2)
						loadedCount++
					}
				}
			} catch {
				continue
			}
		}

		return (loadedCount > 0)

	} catch OSError {
		return false
	} catch Error {
		return false
	}
}
